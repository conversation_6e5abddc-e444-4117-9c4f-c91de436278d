extends Node

var total_painted_tiles_in_run: int = 0
var collected_artifacts: Array[ArtifactData] = []

func reset() -> void:
	total_painted_tiles_in_run = 0
	collected_artifacts.clear()

func add_painted_tiles(count: int) -> void:
	if count > 0:
		total_painted_tiles_in_run += count

func get_total_painted_tiles() -> int:
	return total_painted_tiles_in_run

func add_artifact(artifact_data: ArtifactData) -> void:
	if not has_artifact(artifact_data.id):
		collected_artifacts.append(artifact_data)

func has_artifact(artifact_id: StringName) -> bool:
	for artifact in collected_artifacts:
		if artifact.id == artifact_id:
			return true
	return false

func get_collected_artifacts() -> Array[ArtifactData]:
	return collected_artifacts
