class_name PaintSystem
extends Node
signal player_landed_on_painted_tile(paint_component: PaintComponent)


@export var paint_refill_scene: PackedScene
@export var level_node: Node2D
@export var tile_query_system: TileQuerySystem
@export var level_progress_system: LevelProgressSystem
@export var player_service: PlayerService

var _player_node: Node2D
var _player_paint_component: PaintComponent
var _player_ability_component: AbilityComponent
var _player_movement_component: MovementComponent

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node_param: Node2D) -> void:
	_player_node = player_service.get_player_node()
	_player_paint_component = player_service.get_paint_component()
	_player_ability_component = player_service.get_ability_component()
	_player_movement_component = player_service.get_movement_component()
	_player_movement_component.movement_completed.connect(_on_player_movement_completed)

func _physics_process(_delta: float) -> void:
	if not is_instance_valid(_player_paint_component):
		return

	_handle_refills()

func _try_paint_tile(paint_component: PaintComponent, tile_to_paint: ColorTile, color: Color) -> bool:
	if not _can_process_paint_request(paint_component, tile_to_paint):
		return false

	tile_to_paint.paint(color)
	_consume_paint(paint_component)
	return true

func _add_paint(paint_component: PaintComponent, amount: int) -> void:
	paint_component.current_paint += amount

func _on_player_movement_completed() -> void:
	var current_tile: Node2D = tile_query_system.get_tile_at_global_pos(_player_node.global_position)
	if not is_instance_valid(current_tile) or current_tile is not ColorTile:
		return

	var color_tile: ColorTile = current_tile as ColorTile
	var painted_successfully: bool = _try_paint_tile(_player_paint_component, color_tile, StatService.get_paint_color())

	if not painted_successfully and color_tile.is_painted():
		player_landed_on_painted_tile.emit(_player_paint_component)

func _can_process_paint_request(paint_component: PaintComponent, tile_to_paint: ColorTile) -> bool:
	if not is_instance_valid(tile_to_paint):
		return false
	if tile_to_paint.is_painted():
		return false
	if not _has_enough_paint(paint_component):
		return false

	return true

func _has_enough_paint(paint_component: PaintComponent) -> bool:
	return paint_component.current_paint > 0

func _consume_paint(paint_component: PaintComponent) -> void:
	paint_component.current_paint -= 1

func _handle_refills() -> void:
	var target_drop_count: int = _player_ability_component.get_passive_value_modifier(&"paint_drop_count", 1)

	var refill_nodes: Array[Node] = get_tree().get_nodes_in_group(&"paint_refills")
	var current_drop_count: int = refill_nodes.size()

	if current_drop_count < target_drop_count:
		var drops_to_spawn: int = target_drop_count - current_drop_count
		_spawn_refills(drops_to_spawn)

	for node: Node in refill_nodes:
		_process_potential_collection(node)

func _spawn_refills(count: int) -> void:
	var existing_refills: Array[Node] = get_tree().get_nodes_in_group(&"paint_refills")
	var occupied_positions: Dictionary = {}
	for refill_node: Node2D in existing_refills:
		occupied_positions[refill_node.global_position] = true

	var potential_tiles: Array[Node2D] = level_progress_system.get_safe_tiles()
	var available_tiles: Array[Node2D] = []
	for tile: Node2D in potential_tiles:
		if not occupied_positions.has(tile.global_position):
			available_tiles.append(tile)

	for i in range(count):
		if available_tiles.is_empty():
			break

		var random_tile: Node2D = available_tiles.pick_random()
		available_tiles.erase(random_tile)

		var spawn_position: Vector2 = random_tile.global_position
		var new_drop: Node2D = paint_refill_scene.instantiate() as Node2D
		level_node.add_child(new_drop)
		new_drop.global_position = spawn_position

func _process_potential_collection(refill_node: Node) -> void:
	var component: PaintRefillComponent = refill_node.find_child(&"PaintRefillComponent", true, false)
	if component == null or component.data == null:
		return

	var area: Area2D = refill_node.find_child(&"InteractionArea", true, false)
	if area == null:
		return

	var bodies: Array[Node2D] = area.get_overlapping_bodies()
	for body: Node2D in bodies:
		var paint_component: PaintComponent = body.find_child(&"PaintComponent", true, false)

		if paint_component != null:
			_add_paint(paint_component, component.data.refill_amount)
			refill_node.queue_free()
			return
