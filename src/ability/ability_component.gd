class_name AbilityComponent
extends Node

var ABILITY_LOGIC_MAP := {WallPhaseAbilityData: WallPhaseAbility, RestorePaintAbilityData: RestorePaintAbility, MaintainNormalTimeAbilityData: MaintainNormalTimeAbility, InvertedControlsAbilityData: InvertedControlsAbility}


func get_ability(ability_type: GDScript) -> Ability:
	for child: Node in get_children():
		if child is Ability:
			var ability: Ability = child as Ability
			if ability.data != null and ability.data.get_script() == ability_type:
				return ability
	return null

func has_ability(ability_type: GDScript) -> bool:
	return get_ability(ability_type) != null

func remove_ability(ability: Ability) -> void:
	if ability != null and ability.get_parent() == self:
		ability.queue_free()

func add_ability_from_data(ability_data: AbilityData) -> void:
	var ability_logic_class: Object = ABILITY_LOGIC_MAP.get(ability_data.get_script())
	var ability_node: Ability
	if ability_logic_class != null:
		ability_node = ability_logic_class.call("new")
	else:
		ability_node = Ability.new()
	ability_node.data = ability_data
	add_child(ability_node)
	if ability_node.has_method("initialize"):
		var parent_2d := get_parent() as Node2D
		if parent_2d != null:
			ability_node.initialize(parent_2d)


func get_passive_value_modifier(value_id: StringName, default_value: Variant) -> Variant:
	if value_id == &"paint_drop_count":
		var ability: Ability = get_ability(MultiDropAbilityData)
		if is_instance_valid(ability):
			var data := ability.data as MultiDropAbilityData
			if data != null:
				return data.drop_count
		return default_value
	return default_value
