[gd_resource type="Resource" script_class="ArtifactData" load_steps=4 format=3 uid="uid://dltpl8av1vj5h"]

[ext_resource type="Script" uid="uid://d14cb5snb7hab" path="res://src/artifact/artifact_data.gd" id="1_cqhy4"]
[ext_resource type="Script" uid="uid://dcms2verply6s" path="res://src/ability/restore_paint_ability_data.gd" id="1_wjswu"]

[sub_resource type="Resource" id="Resource_y8l5m"]
script = ExtResource("1_wjswu")
paint_to_restore = 1
metadata/_custom_type_script = "uid://dcms2verply6s"

[resource]
script = ExtResource("1_cqhy4")
id = &"test_artifact"
display_name = "Тестовый артефакт"
description = "Тестовое описание"
ability_data = SubResource("Resource_y8l5m")
metadata/_custom_type_script = "uid://d14cb5snb7hab"
