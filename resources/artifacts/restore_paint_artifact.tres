[gd_resource type="Resource" script_class="ArtifactData" load_steps=5 format=3 uid="uid://cw0c3qkt2a4i2"]

[ext_resource type="Script" uid="uid://d14cb5snb7hab" path="res://src/artifact/artifact_data.gd" id="1"]
[ext_resource type="Script" uid="uid://dcms2verply6s" path="res://src/ability/restore_paint_ability_data.gd" id="2"]
[ext_resource type="Texture2D" uid="uid://cm7p58sgwcf46" path="res://assets/cog_bronze.png" id="2_av0oc"]

[sub_resource type="Resource" id="RestorePaintData"]
script = ExtResource("2")
paint_to_restore = 1

[resource]
script = ExtResource("1")
id = "restore_paint"
display_name = "Капля возврата"
description = "Возвращает краску при шаге по уже окрашенной плитке."
icon = ExtResource("2_av0oc")
ability_data = SubResource("RestorePaintData")
